using System;
using System.ComponentModel;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using FlipBookApp.ViewModels;

namespace FlipBookApp
{
    public partial class MainWindow : Window
    {
        private MainViewModel? _viewModel;
        private Border? _sidebar;
        private TranslateTransform? _sidebarTransform;

        public MainWindow()
        {
            InitializeComponent();
            _viewModel = new MainViewModel();
            DataContext = _viewModel;

            Loaded += OnWindowLoaded;
            _viewModel.PropertyChanged += OnViewModelPropertyChanged;

            // Add keyboard navigation
            KeyDown += OnKeyDown;
        }

        private void OnWindowLoaded(object sender, RoutedEventArgs e)
        {
            // Find the sidebar elements
            _sidebar = FindName("Sidebar") as Border;
            if (_sidebar != null)
            {
                _sidebarTransform = _sidebar.RenderTransform as TranslateTransform;
                if (_sidebarTransform == null)
                {
                    _sidebarTransform = new TranslateTransform(-300, 0);
                    _sidebar.RenderTransform = _sidebarTransform;
                }
                else
                {
                    _sidebarTransform.X = -300; // Start hidden
                }
                Console.WriteLine("Sidebar initialized successfully");
            }
            else
            {
                Console.WriteLine("Warning: Sidebar not found!");
            }
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MainViewModel.IsSidebarOpen))
            {
                AnimateSidebar();
            }
        }

        private void AnimateSidebar()
        {
            if (_sidebarTransform == null || _viewModel == null) return;

            try
            {
                var targetX = _viewModel.IsSidebarOpen ? 0 : -300;

                var animation = new DoubleAnimation
                {
                    To = targetX,
                    Duration = TimeSpan.FromMilliseconds(300),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                };

                _sidebarTransform.BeginAnimation(TranslateTransform.XProperty, animation);
            }
            catch (Exception ex)
            {
                // Fallback: Just set the position directly without animation
                Console.WriteLine($"Animation error: {ex.Message}");
                _sidebarTransform.X = _viewModel.IsSidebarOpen ? 0 : -300;
            }
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            if (_viewModel == null) return;

            switch (e.Key)
            {
                case Key.Left:
                case Key.PageUp:
                    if (_viewModel.PreviousPageCommand.CanExecute(null))
                    {
                        _viewModel.PreviousPageCommand.Execute(null);
                        e.Handled = true;
                    }
                    break;

                case Key.Right:
                case Key.PageDown:
                case Key.Space:
                    if (_viewModel.NextPageCommand.CanExecute(null))
                    {
                        _viewModel.NextPageCommand.Execute(null);
                        e.Handled = true;
                    }
                    break;

                case Key.Home:
                    _viewModel.GoToPageCommand.Execute(1);
                    e.Handled = true;
                    break;

                case Key.End:
                    _viewModel.GoToPageCommand.Execute(_viewModel.TotalPages);
                    e.Handled = true;
                    break;

                case Key.Escape:
                    if (_viewModel.IsSidebarOpen)
                    {
                        _viewModel.ToggleSidebarCommand.Execute(null);
                        e.Handled = true;
                    }
                    break;
            }
        }

        private void OnOverlayClick(object sender, MouseButtonEventArgs e)
        {
            // Close sidebar when clicking on overlay
            if (DataContext is MainViewModel viewModel)
            {
                viewModel.ToggleSidebarCommand.Execute(null);
            }
        }
    }

    // Converter for half width calculation
    public class HalfWidthConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double width)
            {
                return width / 2;
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Converter for chapter font weight
    public class ChapterFontWeightConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isChapter && isChapter)
            {
                return FontWeights.SemiBold;
            }
            return FontWeights.Normal;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Converter for chapter font size
    public class ChapterFontSizeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isChapter && isChapter)
            {
                return 14.0;
            }
            return 13.0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Converter for level-based margin
    public class LevelToMarginConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int level)
            {
                var leftMargin = level * 16; // 16 pixels per level
                return new Thickness(leftMargin, 0, 0, 0);
            }
            return new Thickness(0);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Inverse boolean to visibility converter
    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Boolean to cursor converter
    public class BooleanToCursorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && boolValue)
            {
                return Cursors.Hand;
            }
            return Cursors.Arrow;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
