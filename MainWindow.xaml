<Window x:Class="FlipBookApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:controls="clr-namespace:FlipBookApp.Controls"
        xmlns:local="clr-namespace:FlipBookApp"
        mc:Ignorable="d"
        Title="{Binding DocumentTitle}"
        Height="600" Width="1000"
        MinHeight="500" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Custom converters -->
        <local:HalfWidthConverter x:Key="HalfWidthConverter"/>
        <local:ChapterFontWeightConverter x:Key="ChapterFontWeightConverter"/>
        <local:ChapterFontSizeConverter x:Key="ChapterFontSizeConverter"/>
        <local:LevelToMarginConverter x:Key="LevelToMarginConverter"/>
        <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>

        <!-- Custom button style for menu button -->
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#20FFFFFF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#40FFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>


    </Window.Resources>



    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Top Menu Bar -->
        <materialDesign:ColorZone Grid.Row="0"
                                  Mode="PrimaryMid"
                                  Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Menu Button -->
                <Button Grid.Column="0"
                        Command="{Binding ToggleSidebarCommand}"
                        ToolTip="Toggle Table of Contents"
                        Width="40" Height="40"
                        Background="Transparent"
                        BorderThickness="0"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button" BasedOn="{StaticResource MenuButtonStyle}">
                            <!-- Fallback to custom style if MaterialDesign not available -->
                        </Style>
                    </Button.Style>
                    <controls:HamburgerIcon Foreground="White"/>
                </Button>

                <!-- Title -->
                <TextBlock Grid.Column="1"
                          Text="{Binding DocumentTitle}"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Center"
                          FontSize="18"
                          FontWeight="Medium"/>

                <!-- Load PDF Button -->
                <Button Grid.Column="2"
                        Content="Load PDF"
                        Command="{Binding LoadPdfCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        materialDesign:ButtonAssist.CornerRadius="4"/>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content Area -->
        <Grid Grid.Row="1">
            <!-- PDF Viewer -->
            <controls:PdfFlipControl DataContext="{Binding}"/>

            <!-- Sidebar Overlay -->
            <Border Background="#80000000"
                    Visibility="{Binding IsSidebarOpen, Converter={StaticResource BooleanToVisibilityConverter}}"
                    MouseLeftButtonDown="OnOverlayClick"/>

            <!-- Sidebar -->
            <Border x:Name="Sidebar"
                    Width="300"
                    HorizontalAlignment="Left"
                    Background="{DynamicResource MaterialDesignPaper}"
                    materialDesign:ShadowAssist.ShadowDepth="Depth3"
                    RenderTransformOrigin="0,0">
                <Border.RenderTransform>
                    <TranslateTransform X="-300"/>
                </Border.RenderTransform>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Sidebar Header -->
                    <materialDesign:ColorZone Grid.Row="0"
                                              Mode="PrimaryMid"
                                              Padding="16,12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0"
                                      Text="Table of Contents"
                                      FontSize="16"
                                      FontWeight="Medium"
                                      VerticalAlignment="Center"/>

                            <Button Grid.Column="1"
                                    Command="{Binding ToggleSidebarCommand}"
                                    Width="32" Height="32"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource MenuButtonStyle}">
                                        <!-- Fallback to custom style if MaterialDesign not available -->
                                    </Style>
                                </Button.Style>
                                <controls:CloseIcon Foreground="White"/>
                            </Button>
                        </Grid>
                    </materialDesign:ColorZone>

                    <!-- Table of Contents -->
                    <ScrollViewer Grid.Row="1"
                                  VerticalScrollBarVisibility="Auto"
                                  Padding="8">
                        <ItemsControl ItemsSource="{Binding TableOfContents}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Left"
                                            Padding="16,8"
                                            Margin="0,2"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Command="{Binding DataContext.NavigateToPageCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding Page}"
                                            Style="{StaticResource MaterialDesignFlatButton}">
                                        <Button.Content>
                                            <StackPanel Orientation="Vertical"
                                                       HorizontalAlignment="Left">
                                                <TextBlock Text="{Binding Title}"
                                                          FontWeight="{Binding IsChapter, Converter={StaticResource ChapterFontWeightConverter}}"
                                                          FontSize="{Binding IsChapter, Converter={StaticResource ChapterFontSizeConverter}}"
                                                          Margin="{Binding Level, Converter={StaticResource LevelToMarginConverter}}"
                                                          TextWrapping="Wrap"/>
                                                <TextBlock Text="{Binding Page, StringFormat='Page {0}'}"
                                                          FontSize="11"
                                                          Foreground="Gray"
                                                          Margin="{Binding Level, Converter={StaticResource LevelToMarginConverter}}"/>
                                            </StackPanel>
                                        </Button.Content>
                                    </Button>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
