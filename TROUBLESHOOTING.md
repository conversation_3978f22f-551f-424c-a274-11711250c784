# Troubleshooting Guide

## Common Issues and Solutions

### 1. "Unable to load DLL 'pdfium.dll'" Error

**Problem**: You see an error message like:
```
System.DllNotFoundException: 'Unable to load DLL 'pdfium.dll' or one of its dependencies: The specified module could not be found. (0x8007007E)'
```

**Solutions**:

#### Option 1: Install Visual C++ Redistributable (Recommended)
1. Download and install the latest **Microsoft Visual C++ Redistributable for Visual Studio 2015-2022** (x64 version)
2. Download from: https://aka.ms/vs/17/release/vc_redist.x64.exe
3. Restart the application after installation

#### Option 2: Check Application Architecture
- Ensure you're running the x64 version of the application
- The application is built for 64-bit systems only

#### Option 3: Manual DLL Verification
1. Navigate to the application folder
2. Check that the `x64` subfolder exists and contains `pdfium.dll`
3. If missing, try rebuilding the application with `dotnet build -c Release`

### 2. Window Controls Not Visible

**Problem**: Cannot see minimize, maximize, or close buttons.

**Solution**: The application now starts maximized by default. You can:
- Press `Alt + Space` to access window controls via keyboard
- Press `Alt + F4` to close the application
- Use `Windows + Down Arrow` to restore the window

### 3. Application Performance Issues

**Solutions**:
- Ensure you have sufficient RAM (recommended: 4GB+)
- Close other applications to free up memory
- Try loading smaller PDF files first

### 4. PDF Loading Issues

**Common causes**:
- **Corrupted PDF**: Try opening the PDF in another application first
- **Password-protected PDF**: Currently not supported
- **Very large PDF files**: May take longer to load

### 5. Table of Contents Not Showing

**Solutions**:
1. **For PDFs with embedded bookmarks**: Should appear automatically
2. **For PDFs without bookmarks**: Update the `table_content.json` file with the correct structure
3. **Check JSON format**: Ensure the JSON file is valid and follows the expected structure

## Getting Help

If you continue to experience issues:

1. **Check the console output** for detailed error messages
2. **Verify system requirements**:
   - Windows 10/11 (64-bit)
   - .NET 8 Runtime
   - Visual C++ Redistributable 2015-2022
3. **Try with the included test PDF** first to isolate the issue

## System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Framework**: .NET 8.0 Runtime
- **Memory**: 2GB RAM minimum, 4GB recommended
- **Dependencies**: Visual C++ Redistributable 2015-2022 (x64)

## Contact

For additional support, please provide:
- Operating system version
- Error message (full text)
- PDF file size and source (if applicable)
- Steps to reproduce the issue
