<UserControl x:Class="FlipBookApp.Controls.PdfFlipControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- Page flip animation storyboard -->
        <Storyboard x:Key="FlipToNextPage">
            <DoubleAnimation Storyboard.TargetName="CurrentPageTransform"
                           Storyboard.TargetProperty="ScaleX"
                           From="1" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseInOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <Storyboard x:Key="FlipToPreviousPage">
            <DoubleAnimation Storyboard.TargetName="CurrentPageTransform"
                           Storyboard.TargetProperty="ScaleX"
                           From="1" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseInOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <Storyboard x:Key="ShowPage">
            <DoubleAnimation Storyboard.TargetName="CurrentPageTransform"
                           Storyboard.TargetProperty="ScaleX"
                           From="0" To="1" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseInOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- PDF Page Display Area -->
        <Border Grid.Row="0"
                Background="#F5F5F5"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                Margin="10">
            <Grid>
                <!-- Loading indicator -->
                <ProgressBar x:Name="LoadingProgress"
                           IsIndeterminate="True"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Width="200"/>

                <!-- PDF Page Image -->
                <Image x:Name="PdfPageImage"
                       Stretch="Uniform"
                       RenderTransformOrigin="0.5,0.5"
                       Visibility="{Binding IsPdfLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Image.RenderTransform>
                        <ScaleTransform x:Name="CurrentPageTransform" ScaleX="1" ScaleY="1"/>
                    </Image.RenderTransform>
                </Image>

                <!-- No PDF loaded message -->
                <StackPanel VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Visibility="{Binding IsPdfLoaded, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <TextBlock Text="📄"
                             FontSize="48"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,10"/>
                    <TextBlock Text="No PDF loaded"
                             FontSize="16"
                             HorizontalAlignment="Center"
                             Foreground="Gray"/>
                    <TextBlock Text="Click 'Load PDF' to get started"
                             FontSize="12"
                             HorizontalAlignment="Center"
                             Foreground="Gray"
                             Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Navigation overlays -->
                <Grid Visibility="{Binding IsPdfLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <!-- Previous page area (left half) -->
                    <Button x:Name="PreviousPageArea"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Left"
                            Command="{Binding PreviousPageCommand}"
                            Cursor="Hand"
                            ToolTip="Previous page">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}">
                                    <ContentPresenter/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- Next page area (right half) -->
                    <Button x:Name="NextPageArea"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Right"
                            Command="{Binding NextPageCommand}"
                            Cursor="Hand"
                            ToolTip="Next page">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}">
                                    <ContentPresenter/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </Grid>
            </Grid>
        </Border>

        <!-- Page Navigation Controls -->
        <StackPanel Grid.Row="1"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Margin="10">

            <Button Content="⏮"
                    Command="{Binding PreviousPageCommand}"
                    Width="40" Height="30"
                    Margin="5"
                    ToolTip="Previous page"/>

            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                <TextBlock Text="Page " VerticalAlignment="Center"/>
                <TextBox x:Name="PageNumberTextBox"
                         Text="{Binding CurrentPage, UpdateSourceTrigger=PropertyChanged}"
                         Width="50"
                         Height="25"
                         TextAlignment="Center"
                         VerticalContentAlignment="Center"
                         Margin="5,0"/>
                <TextBlock Text=" of " VerticalAlignment="Center"/>
                <TextBlock Text="{Binding TotalPages}" VerticalAlignment="Center"/>
            </StackPanel>

            <Button Content="⏭"
                    Command="{Binding NextPageCommand}"
                    Width="40" Height="30"
                    Margin="5"
                    ToolTip="Next page"/>
        </StackPanel>
    </Grid>
</UserControl>
