<UserControl x:Class="FlipBookApp.Controls.PdfFlipControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:FlipBookApp"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>

        <!-- Page flip animation storyboard -->
        <Storyboard x:Key="FlipToNextPage">
            <DoubleAnimation Storyboard.TargetName="CurrentPageTransform"
                           Storyboard.TargetProperty="ScaleX"
                           From="1" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseInOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <Storyboard x:Key="FlipToPreviousPage">
            <DoubleAnimation Storyboard.TargetName="CurrentPageTransform"
                           Storyboard.TargetProperty="ScaleX"
                           From="1" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseInOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <Storyboard x:Key="ShowPage">
            <DoubleAnimation Storyboard.TargetName="CurrentPageTransform"
                           Storyboard.TargetProperty="ScaleX"
                           From="0" To="1" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseInOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- PDF Page Display Area -->
        <Border Grid.Row="0"
                Background="#F5F5F5"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                Margin="10">
            <Grid>
                <!-- Loading indicator -->
                <ProgressBar x:Name="LoadingProgress"
                           IsIndeterminate="True"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Width="200"/>

                <!-- PDF Page Image -->
                <Image x:Name="PdfPageImage"
                       Stretch="Uniform"
                       RenderTransformOrigin="0.5,0.5"
                       Visibility="{Binding IsPdfLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Image.RenderTransform>
                        <ScaleTransform x:Name="CurrentPageTransform" ScaleX="1" ScaleY="1"/>
                    </Image.RenderTransform>
                </Image>

                <!-- No PDF loaded message -->
                <StackPanel VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Visibility="{Binding IsPdfLoaded, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <TextBlock Text="📄"
                             FontSize="48"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,10"/>
                    <TextBlock Text="No PDF loaded"
                             FontSize="16"
                             HorizontalAlignment="Center"
                             Foreground="Gray"/>
                    <TextBlock Text="Click 'Load PDF' to get started"
                             FontSize="12"
                             HorizontalAlignment="Center"
                             Foreground="Gray"
                             Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Navigation overlays -->
                <Grid x:Name="NavigationOverlay"
                      Visibility="{Binding IsPdfLoaded, Converter={StaticResource BooleanToVisibilityConverter}}"
                      Background="Transparent">

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Previous page area (left half) -->
                    <Button x:Name="PreviousPageArea"
                            Grid.Column="0"
                            Background="Transparent"
                            BorderThickness="0"
                            Command="{Binding PreviousPageCommand}"
                            ToolTip="Previous page"
                            Cursor="Hand">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        x:Name="HoverBorder">
                                    <!-- Left arrow indicator on hover -->
                                    <Grid x:Name="ArrowIndicator"
                                          HorizontalAlignment="Left"
                                          VerticalAlignment="Center"
                                          Margin="20,0,0,0"
                                          Opacity="0">
                                        <Ellipse Width="40" Height="40"
                                                Fill="#80000000"
                                                Stroke="White"
                                                StrokeThickness="1"/>
                                        <Path Data="M15,12 L9,18 L15,24"
                                              Stroke="White"
                                              StrokeThickness="2"/>
                                    </Grid>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsMouseOver" Value="True"/>
                                            <Condition Property="IsEnabled" Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <MultiTrigger.EnterActions>
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimation Storyboard.TargetName="ArrowIndicator"
                                                                   Storyboard.TargetProperty="Opacity"
                                                                   To="1" Duration="0:0:0.2"/>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </MultiTrigger.EnterActions>
                                        <MultiTrigger.ExitActions>
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimation Storyboard.TargetName="ArrowIndicator"
                                                                   Storyboard.TargetProperty="Opacity"
                                                                   To="0" Duration="0:0:0.2"/>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </MultiTrigger.ExitActions>
                                    </MultiTrigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- Next page area (right half) -->
                    <Button x:Name="NextPageArea"
                            Grid.Column="1"
                            Background="Transparent"
                            BorderThickness="0"
                            Command="{Binding NextPageCommand}"
                            ToolTip="Next page"
                            Cursor="Hand">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        x:Name="HoverBorder">
                                    <!-- Right arrow indicator on hover -->
                                    <Grid x:Name="ArrowIndicator"
                                          HorizontalAlignment="Right"
                                          VerticalAlignment="Center"
                                          Margin="0,0,20,0"
                                          Opacity="0">
                                        <Ellipse Width="40" Height="40"
                                                Fill="#80000000"
                                                Stroke="White"
                                                StrokeThickness="1"/>
                                        <Path Data="M9,12 L15,18 L9,24"
                                              Stroke="White"
                                              StrokeThickness="2"/>
                                    </Grid>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsMouseOver" Value="True"/>
                                            <Condition Property="IsEnabled" Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <MultiTrigger.EnterActions>
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimation Storyboard.TargetName="ArrowIndicator"
                                                                   Storyboard.TargetProperty="Opacity"
                                                                   To="1" Duration="0:0:0.2"/>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </MultiTrigger.EnterActions>
                                        <MultiTrigger.ExitActions>
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimation Storyboard.TargetName="ArrowIndicator"
                                                                   Storyboard.TargetProperty="Opacity"
                                                                   To="0" Duration="0:0:0.2"/>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </MultiTrigger.ExitActions>
                                    </MultiTrigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </Grid>
            </Grid>
        </Border>

        <!-- Page Navigation Controls -->
        <StackPanel Grid.Row="1"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Margin="10">

            <Button Content="⏮"
                    Command="{Binding PreviousPageCommand}"
                    Width="40" Height="30"
                    Margin="5"
                    ToolTip="Previous page"/>

            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                <TextBlock Text="Page " VerticalAlignment="Center"/>
                <TextBox x:Name="PageNumberTextBox"
                         Text="{Binding CurrentPage, UpdateSourceTrigger=PropertyChanged}"
                         Width="50"
                         Height="25"
                         TextAlignment="Center"
                         VerticalContentAlignment="Center"
                         Margin="5,0"/>
                <TextBlock Text=" of " VerticalAlignment="Center"/>
                <TextBlock Text="{Binding TotalPages}" VerticalAlignment="Center"/>
            </StackPanel>

            <Button Content="⏭"
                    Command="{Binding NextPageCommand}"
                    Width="40" Height="30"
                    Margin="5"
                    ToolTip="Next page"/>
        </StackPanel>
    </Grid>
</UserControl>
