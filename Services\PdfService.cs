using System;
using System.IO;
using System.Threading.Tasks;
using Pd<PERSON>umViewer;

namespace FlipBookApp.Services
{
    public class PdfService : IDisposable
    {
        private PdfDocument? _document;
        private bool _disposed = false;

        public PdfDocument? Document => _document;
        public int PageCount => _document?.PageCount ?? 0;
        public string? CurrentFilePath { get; private set; }

        public async Task<bool> LoadPdfAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"PDF file not found: {filePath}");
                }

                // Dispose previous document if any
                _document?.Dispose();

                // Load new document
                _document = await Task.Run(() =>
                {
                    try
                    {
                        return PdfDocument.Load(filePath);
                    }
                    catch (System.DllNotFoundException dllEx)
                    {
                        throw new InvalidOperationException(
                            "PDF rendering library not found. Please ensure the application is properly installed with all dependencies.",
                            dllEx);
                    }
                    catch (Exception ex)
                    {
                        throw new InvalidOperationException($"Failed to load PDF: {ex.Message}", ex);
                    }
                });

                CurrentFilePath = filePath;
                return _document != null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading PDF: {ex.Message}");
                throw; // Re-throw to let the UI handle it
            }
        }

        public System.Drawing.Image? RenderPage(int pageIndex, int width = 800, int height = 600, double qualityMultiplier = 1.0)
        {
            if (_document == null || pageIndex < 0 || pageIndex >= _document.PageCount)
            {
                return null;
            }

            try
            {
                // Calculate DPI based on quality multiplier for crisp text
                // Higher quality multiplier = higher DPI = better text quality
                var baseDpi = 96;
                var renderDpi = (int)(baseDpi * Math.Max(1.0, qualityMultiplier));

                // Cap DPI to prevent excessive memory usage
                renderDpi = Math.Min(renderDpi, 300); // Max 300 DPI

                return _document.Render(pageIndex, width, height, renderDpi, renderDpi, false);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering page {pageIndex}: {ex.Message}");
                return null;
            }
        }

        public bool HasTableOfContents()
        {
            return _document?.Bookmarks != null && _document.Bookmarks.Count > 0;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _document?.Dispose();
                _document = null;
                _disposed = true;
            }
        }

        ~PdfService()
        {
            Dispose(false);
        }
    }
}
