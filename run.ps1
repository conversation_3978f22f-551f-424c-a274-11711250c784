# PowerShell script to run the PDF FlipBook Viewer
Write-Host "Starting PDF FlipBook Viewer..." -ForegroundColor Green
Write-Host ""

# Check if the executable exists
$exePath = "bin\Debug\net8.0-windows\FlipBookApp.exe"
if (-not (Test-Path $exePath)) {
    Write-Host "Building the application..." -ForegroundColor Yellow
    dotnet build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed! Please check the error messages above." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Run the application
Write-Host "Launching FlipBookApp..." -ForegroundColor Green
Start-Process -FilePath $exePath

Write-Host "Application started successfully!" -ForegroundColor Green
Write-Host "You can close this window." -ForegroundColor Gray
Read-Host "Press Enter to exit"
