<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="PdfiumViewer" Version="2.13.0" />
    <PackageReference Include="PdfiumViewer.Native.x86_64.v8-xfa" Version="2018.4.8.256" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="table_content.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="test.pdf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <!-- Include native dependencies for single-file publish -->
  <Target Name="CopyNativeDependencies" AfterTargets="Build">
    <ItemGroup>
      <NativeLibs Include="$(OutputPath)x64\*.dll" />
    </ItemGroup>
    <Copy SourceFiles="@(NativeLibs)" DestinationFolder="$(OutputPath)" SkipUnchangedFiles="true" />
  </Target>

  <!-- For publish, copy native DLLs to publish root and exclude from single file -->
  <Target Name="CopyNativeDependenciesForPublish" AfterTargets="GenerateSingleFileBundle">
    <ItemGroup>
      <NativeLibsForPublish Include="$(PublishDir)x64\*.dll" />
    </ItemGroup>
    <Copy SourceFiles="@(NativeLibsForPublish)" DestinationFolder="$(PublishDir)" SkipUnchangedFiles="true" />
    <Delete Files="@(NativeLibsForPublish)" />
    <RemoveDir Directories="$(PublishDir)x64" Condition="Exists('$(PublishDir)x64')" />
  </Target>

  <!-- Exclude native DLLs from single file but keep them as separate files -->
  <ItemGroup>
    <Content Include="$(OutputPath)pdfium.dll" Condition="Exists('$(OutputPath)pdfium.dll')">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
  </ItemGroup>



</Project>
