using System.Collections.Generic;
using Newtonsoft.Json;

namespace FlipBookApp.Models
{
    public class TableOfContentsModel
    {
        [JsonProperty("catalog")]
        public Catalog Catalog { get; set; } = new();
    }

    public class Catalog
    {
        [JsonProperty("title")]
        public string Title { get; set; } = string.Empty;

        [JsonProperty("chapters")]
        public List<Chapter> Chapters { get; set; } = new();
    }

    public class Chapter
    {
        [JsonProperty("chapter")]
        public int ChapterNumber { get; set; }

        [JsonProperty("title")]
        public string Title { get; set; } = string.Empty;

        [JsonProperty("courses")]
        public List<Course> Courses { get; set; } = new();
    }

    public class Course
    {
        [JsonProperty("title")]
        public string Title { get; set; } = string.Empty;

        [JsonProperty("page")]
        public int Page { get; set; }
    }

    public class TocItem
    {
        public string Title { get; set; } = string.Empty;
        public int Page { get; set; }
        public int Level { get; set; } // 0 for chapter, 1 for course
        public bool IsChapter => Level == 0;
    }
}
