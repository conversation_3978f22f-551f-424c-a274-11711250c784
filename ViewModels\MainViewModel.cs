using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FlipBookApp.Models;
using FlipBookApp.Services;
using Microsoft.Win32;

namespace FlipBookApp.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly PdfService _pdfService;
        private readonly TableOfContentsService _tocService;

        [ObservableProperty]
        private int _currentPage = 1;

        [ObservableProperty]
        private int _totalPages = 0;

        [ObservableProperty]
        private bool _isPdfLoaded = false;

        [ObservableProperty]
        private bool _isSidebarOpen = false;

        [ObservableProperty]
        private string _documentTitle = "PDF FlipBook Viewer";

        [ObservableProperty]
        private ObservableCollection<TocItem> _tableOfContents = new();

        [ObservableProperty]
        private bool _isLoading = false;

        [ObservableProperty]
        private double _zoomLevel = 1.0;

        [ObservableProperty]
        private string _zoomPercentage = "100%";

        public ICommand LoadPdfCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand GoToPageCommand { get; }
        public ICommand ToggleSidebarCommand { get; }
        public ICommand NavigateToPageCommand { get; }
        public ICommand ZoomInCommand { get; }
        public ICommand ZoomOutCommand { get; }
        public ICommand ZoomToFitCommand { get; }
        public ICommand ZoomToWidthCommand { get; }
        public ICommand SetZoomCommand { get; }

        public MainViewModel()
        {
            _pdfService = new PdfService();
            _tocService = new TableOfContentsService();

            LoadPdfCommand = new AsyncRelayCommand(LoadPdfAsync);
            NextPageCommand = new RelayCommand(NextPage, CanGoToNextPage);
            PreviousPageCommand = new RelayCommand(PreviousPage, CanGoToPreviousPage);
            GoToPageCommand = new RelayCommand<int>(GoToPage);
            ToggleSidebarCommand = new RelayCommand(ToggleSidebar);
            NavigateToPageCommand = new RelayCommand<int>(NavigateToPage);
            ZoomInCommand = new RelayCommand(ZoomIn, CanZoomIn);
            ZoomOutCommand = new RelayCommand(ZoomOut, CanZoomOut);
            ZoomToFitCommand = new RelayCommand(ZoomToFit);
            ZoomToWidthCommand = new RelayCommand(ZoomToWidth);
            SetZoomCommand = new RelayCommand<double>(SetZoom);

            // Load default TOC on startup
            _ = LoadTableOfContentsAsync();
        }

        private async Task LoadPdfAsync()
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "PDF files (*.pdf)|*.pdf|All files (*.*)|*.*",
                Title = "Select a PDF file"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                IsLoading = true;

                try
                {
                    var success = await _pdfService.LoadPdfAsync(openFileDialog.FileName);

                    if (success)
                    {
                        IsPdfLoaded = true;
                        TotalPages = _pdfService.PageCount;
                        CurrentPage = 1;
                        DocumentTitle = Path.GetFileNameWithoutExtension(openFileDialog.FileName);

                        // Load table of contents for this PDF
                        await LoadTableOfContentsAsync(openFileDialog.FileName);

                        // Refresh command states
                        ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                        ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
                    }
                }
                catch (InvalidOperationException ex)
                {
                    System.Windows.MessageBox.Show(
                        $"Error loading PDF:\n\n{ex.Message}\n\nPlease try installing the Visual C++ Redistributable or contact support.",
                        "PDF Loading Error",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show(
                        $"Unexpected error loading PDF:\n\n{ex.Message}",
                        "Error",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        private async Task LoadTableOfContentsAsync(string? pdfPath = null)
        {
            try
            {
                var tocItems = await _tocService.GetTableOfContentsAsync(pdfPath);
                TableOfContents.Clear();

                foreach (var item in tocItems)
                {
                    TableOfContents.Add(item);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading table of contents: {ex.Message}");
            }
        }

        private void NextPage()
        {
            if (CanGoToNextPage())
            {
                CurrentPage++;
                ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
            }
        }

        private bool CanGoToNextPage()
        {
            return IsPdfLoaded && CurrentPage < TotalPages;
        }

        private void PreviousPage()
        {
            if (CanGoToPreviousPage())
            {
                CurrentPage--;
                ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
            }
        }

        private bool CanGoToPreviousPage()
        {
            return IsPdfLoaded && CurrentPage > 1;
        }



        private void GoToPage(int pageNumber)
        {
            if (pageNumber >= 1 && pageNumber <= TotalPages)
            {
                CurrentPage = pageNumber;
                ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
            }
        }

        private void ToggleSidebar()
        {
            IsSidebarOpen = !IsSidebarOpen;
        }

        private void NavigateToPage(int pageNumber)
        {
            GoToPage(pageNumber);
            IsSidebarOpen = false; // Close sidebar after navigation
        }

        public PdfService GetPdfService()
        {
            return _pdfService;
        }

        // Zoom functionality
        private const double MinZoom = 0.25; // 25%
        private const double MaxZoom = 5.0;  // 500%
        private const double ZoomStep = 0.25; // 25% increments

        private void ZoomIn()
        {
            if (CanZoomIn())
            {
                SetZoom(ZoomLevel + ZoomStep);
            }
        }

        private bool CanZoomIn()
        {
            return ZoomLevel < MaxZoom;
        }

        private void ZoomOut()
        {
            if (CanZoomOut())
            {
                SetZoom(ZoomLevel - ZoomStep);
            }
        }

        private bool CanZoomOut()
        {
            return ZoomLevel > MinZoom;
        }

        private void SetZoom(double zoomLevel)
        {
            // Clamp zoom level to valid range
            zoomLevel = Math.Max(MinZoom, Math.Min(MaxZoom, zoomLevel));

            if (Math.Abs(ZoomLevel - zoomLevel) > 0.01) // Avoid unnecessary updates
            {
                ZoomLevel = zoomLevel;
                ZoomPercentage = $"{(int)(ZoomLevel * 100)}%";

                // Refresh command states
                ((RelayCommand)ZoomInCommand).NotifyCanExecuteChanged();
                ((RelayCommand)ZoomOutCommand).NotifyCanExecuteChanged();
            }
        }

        private void ZoomToFit()
        {
            // Reset to 100% - this provides a good "fit" for most screens
            SetZoom(1.0);
        }

        private void ZoomToWidth()
        {
            // Set to a zoom level that fits page width well
            SetZoom(1.0);
        }

        protected override void OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs e)
        {
            base.OnPropertyChanged(e);

            if (e.PropertyName == nameof(CurrentPage) || e.PropertyName == nameof(IsPdfLoaded) || e.PropertyName == nameof(TotalPages))
            {
                ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
            }
            else if (e.PropertyName == nameof(ZoomLevel))
            {
                ((RelayCommand)ZoomInCommand).NotifyCanExecuteChanged();
                ((RelayCommand)ZoomOutCommand).NotifyCanExecuteChanged();
            }
        }
    }
}
