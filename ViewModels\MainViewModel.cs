using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FlipBookApp.Models;
using FlipBookApp.Services;
using Microsoft.Win32;

namespace FlipBookApp.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly PdfService _pdfService;
        private readonly TableOfContentsService _tocService;

        [ObservableProperty]
        private int _currentPage = 1;

        [ObservableProperty]
        private int _totalPages = 0;

        [ObservableProperty]
        private bool _isPdfLoaded = false;

        [ObservableProperty]
        private bool _isSidebarOpen = false;

        [ObservableProperty]
        private string _documentTitle = "PDF FlipBook Viewer";

        [ObservableProperty]
        private ObservableCollection<TocItem> _tableOfContents = new();

        [ObservableProperty]
        private bool _isLoading = false;

        public ICommand LoadPdfCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand GoToPageCommand { get; }
        public ICommand ToggleSidebarCommand { get; }
        public ICommand NavigateToPageCommand { get; }

        public MainViewModel()
        {
            _pdfService = new PdfService();
            _tocService = new TableOfContentsService();

            LoadPdfCommand = new AsyncRelayCommand(LoadPdfAsync);
            NextPageCommand = new RelayCommand(NextPage, CanGoToNextPage);
            PreviousPageCommand = new RelayCommand(PreviousPage, CanGoToPreviousPage);
            GoToPageCommand = new RelayCommand<int>(GoToPage);
            ToggleSidebarCommand = new RelayCommand(ToggleSidebar);
            NavigateToPageCommand = new RelayCommand<int>(NavigateToPage);

            // Load default TOC on startup
            _ = LoadTableOfContentsAsync();
        }

        private async Task LoadPdfAsync()
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "PDF files (*.pdf)|*.pdf|All files (*.*)|*.*",
                Title = "Select a PDF file"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                IsLoading = true;

                try
                {
                    var success = await _pdfService.LoadPdfAsync(openFileDialog.FileName);

                    if (success)
                    {
                        IsPdfLoaded = true;
                        TotalPages = _pdfService.PageCount;
                        CurrentPage = 1;
                        DocumentTitle = Path.GetFileNameWithoutExtension(openFileDialog.FileName);

                        // Load table of contents for this PDF
                        await LoadTableOfContentsAsync(openFileDialog.FileName);

                        // Refresh command states
                        ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                        ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
                    }
                }
                catch (InvalidOperationException ex)
                {
                    System.Windows.MessageBox.Show(
                        $"Error loading PDF:\n\n{ex.Message}\n\nPlease try installing the Visual C++ Redistributable or contact support.",
                        "PDF Loading Error",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show(
                        $"Unexpected error loading PDF:\n\n{ex.Message}",
                        "Error",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        private async Task LoadTableOfContentsAsync(string? pdfPath = null)
        {
            try
            {
                var tocItems = await _tocService.GetTableOfContentsAsync(pdfPath);
                TableOfContents.Clear();

                foreach (var item in tocItems)
                {
                    TableOfContents.Add(item);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading table of contents: {ex.Message}");
            }
        }

        private void NextPage()
        {
            if (CanGoToNextPage())
            {
                CurrentPage++;
                ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
            }
        }

        private bool CanGoToNextPage()
        {
            return IsPdfLoaded && CurrentPage < TotalPages;
        }

        private void PreviousPage()
        {
            if (CanGoToPreviousPage())
            {
                CurrentPage--;
                ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
            }
        }

        private bool CanGoToPreviousPage()
        {
            return IsPdfLoaded && CurrentPage > 1;
        }

        private void GoToPage(int pageNumber)
        {
            if (pageNumber >= 1 && pageNumber <= TotalPages)
            {
                CurrentPage = pageNumber;
                ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
            }
        }

        private void ToggleSidebar()
        {
            IsSidebarOpen = !IsSidebarOpen;
        }

        private void NavigateToPage(int pageNumber)
        {
            GoToPage(pageNumber);
            IsSidebarOpen = false; // Close sidebar after navigation
        }

        public PdfService GetPdfService()
        {
            return _pdfService;
        }

        protected override void OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs e)
        {
            base.OnPropertyChanged(e);

            if (e.PropertyName == nameof(CurrentPage))
            {
                ((RelayCommand)NextPageCommand).NotifyCanExecuteChanged();
                ((RelayCommand)PreviousPageCommand).NotifyCanExecuteChanged();
            }
        }
    }
}
