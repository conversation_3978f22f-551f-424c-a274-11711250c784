# PDF FlipBook Viewer

A C# WPF desktop application that provides a flipbook-style PDF viewer similar to Flipsnack, Heyzine, or Issuu services.

## Features

- **PDF Loading**: Load and display PDF files with smooth page transitions
- **Flipbook Animation**: Page flipping animations that simulate reading a real book
- **Table of Contents**: Sidebar drawer with clickable navigation
- **Smart TOC Detection**: Automatically extracts table of contents from PDF bookmarks, falls back to JSON file if not available
- **Modern UI**: Material Design interface with responsive layout
- **Navigation Controls**: Click areas for page navigation, keyboard shortcuts, and page number input

## How to Use

1. **Launch the Application**: Run the executable or use `dotnet run`
2. **Load a PDF**: Click the "Load PDF" button in the top toolbar
3. **Navigate Pages**:
   - Click on the left/right halves of the page to navigate
   - Use the navigation controls at the bottom
   - Type a page number directly
4. **Table of Contents**: Click the menu button (☰) to open the sidebar with TOC
5. **Quick Navigation**: Click any item in the table of contents to jump to that page

## Table of Contents Configuration

The application first checks if the loaded PDF has embedded bookmarks/outline. If not found, it falls back to the `table_content.json` file.

### JSON Structure

```json
{
  "catalog": {
    "title": "Table des matières",
    "chapters": [
      {
        "chapter": 1,
        "title": "Chapter Title",
        "courses": [
          {
            "title": "Section Title",
            "page": 1
          }
        ]
      }
    ]
  }
}
```

To add table of contents for a new PDF:
1. Update the `table_content.json` file with the appropriate structure
2. The application will automatically use this data if the PDF doesn't have embedded bookmarks

## Technical Details

- **Framework**: .NET 8 WPF
- **PDF Rendering**: PdfiumViewer
- **UI Framework**: Material Design Themes
- **Architecture**: MVVM pattern with CommunityToolkit.Mvvm
- **JSON Parsing**: Newtonsoft.Json

## Dependencies

- PdfiumViewer 2.13.0
- MaterialDesignThemes 4.9.0
- Newtonsoft.Json 13.0.3
- Microsoft.Xaml.Behaviors.Wpf 1.1.77
- CommunityToolkit.Mvvm 8.2.2
- System.Drawing.Common 8.0.0

## Building

```bash
dotnet restore
dotnet build -c Release
dotnet run
```

## System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Framework**: .NET 8.0 Runtime
- **Memory**: 2GB RAM minimum, 4GB recommended
- **Dependencies**: Visual C++ Redistributable 2015-2022 (x64)

## Installation

1. **Install .NET 8 Runtime** (if not already installed)
2. **Install Visual C++ Redistributable**: Download from https://aka.ms/vs/17/release/vc_redist.x64.exe
3. **Build or run the application**

## Troubleshooting

If you encounter the error "Unable to load DLL 'pdfium.dll'", please:
1. Install the Visual C++ Redistributable 2015-2022 (x64)
2. Ensure you're running on a 64-bit Windows system
3. See `TROUBLESHOOTING.md` for detailed solutions

## Project Structure

```
FlipBookApp/
├── Models/
│   └── TableOfContentsModel.cs    # Data models for TOC
├── Services/
│   ├── PdfService.cs              # PDF loading and rendering
│   └── TableOfContentsService.cs  # TOC extraction and management
├── ViewModels/
│   └── MainViewModel.cs           # Main application view model
├── Controls/
│   ├── PdfFlipControl.xaml        # Custom PDF viewer control
│   └── PdfFlipControl.xaml.cs
├── MainWindow.xaml                # Main application window
├── MainWindow.xaml.cs
├── App.xaml                       # Application configuration
├── App.xaml.cs
├── FlipBookApp.csproj            # Project file
└── table_content.json           # Default table of contents
```
