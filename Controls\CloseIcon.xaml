<UserControl x:Class="FlipBookApp.Controls.CloseIcon"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Width="16" Height="16">
    <Grid>
        <Rectangle Width="14" Height="2" Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                   RenderTransformOrigin="0.5,0.5" RadiusX="1" RadiusY="1">
            <Rectangle.RenderTransform>
                <RotateTransform Angle="45"/>
            </Rectangle.RenderTransform>
        </Rectangle>
        <Rectangle Width="14" Height="2" Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                   RenderTransformOrigin="0.5,0.5" RadiusX="1" RadiusY="1">
            <Rectangle.RenderTransform>
                <RotateTransform Angle="-45"/>
            </Rectangle.RenderTransform>
        </Rectangle>
    </Grid>
</UserControl>
