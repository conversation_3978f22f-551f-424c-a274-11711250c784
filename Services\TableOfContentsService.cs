using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using FlipBookApp.Models;
using Newtonsoft.Json;
using PdfiumViewer;

namespace FlipBookApp.Services
{
    public class TableOfContentsService
    {
        private const string DefaultTocFile = "table_content.json";

        public async Task<List<TocItem>> GetTableOfContentsAsync(string? pdfPath = null)
        {
            var tocItems = new List<TocItem>();

            // First, try to extract TOC from PDF if provided
            if (!string.IsNullOrEmpty(pdfPath) && File.Exists(pdfPath))
            {
                tocItems = await ExtractTocFromPdfAsync(pdfPath);
            }

            // If no TOC found in PDF or no PDF provided, use JSON file
            if (tocItems.Count == 0)
            {
                tocItems = await LoadTocFromJsonAsync();
            }

            return tocItems;
        }

        private async Task<List<TocItem>> ExtractTocFromPdfAsync(string pdfPath)
        {
            var tocItems = new List<TocItem>();

            try
            {
                await Task.Run(() =>
                {
                    using var document = PdfDocument.Load(pdfPath);

                    // Try to extract bookmarks/outline from PDF
                    var bookmarks = document.Bookmarks;
                    if (bookmarks != null && bookmarks.Count > 0)
                    {
                        ExtractBookmarksRecursively(bookmarks, tocItems, 0);
                    }
                });
            }
            catch (Exception ex)
            {
                // Log error but don't throw - we'll fall back to JSON
                Console.WriteLine($"Error extracting TOC from PDF: {ex.Message}");
            }

            return tocItems;
        }

        private void ExtractBookmarksRecursively(PdfBookmarkCollection bookmarks, List<TocItem> tocItems, int level)
        {
            foreach (PdfBookmark bookmark in bookmarks)
            {
                tocItems.Add(new TocItem
                {
                    Title = bookmark.Title,
                    Page = bookmark.PageIndex + 1, // Convert 0-based to 1-based
                    Level = level
                });

                // Recursively process child bookmarks
                if (bookmark.Children != null && bookmark.Children.Count > 0)
                {
                    ExtractBookmarksRecursively(bookmark.Children, tocItems, level + 1);
                }
            }
        }

        private async Task<List<TocItem>> LoadTocFromJsonAsync()
        {
            var tocItems = new List<TocItem>();

            try
            {
                if (!File.Exists(DefaultTocFile))
                {
                    return tocItems;
                }

                var jsonContent = await File.ReadAllTextAsync(DefaultTocFile);
                var tocModel = JsonConvert.DeserializeObject<TableOfContentsModel>(jsonContent);

                if (tocModel?.Catalog?.Chapters != null)
                {
                    foreach (var chapter in tocModel.Catalog.Chapters)
                    {
                        // Add chapter as a TOC item
                        tocItems.Add(new TocItem
                        {
                            Title = chapter.Title,
                            Page = chapter.Courses.FirstOrDefault()?.Page ?? 1,
                            Level = 0
                        });

                        // Add courses under the chapter
                        foreach (var course in chapter.Courses)
                        {
                            tocItems.Add(new TocItem
                            {
                                Title = course.Title,
                                Page = course.Page,
                                Level = 1
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading TOC from JSON: {ex.Message}");
            }

            return tocItems;
        }
    }
}
