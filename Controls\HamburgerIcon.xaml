<UserControl x:Class="FlipBookApp.Controls.HamburgerIcon"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Width="24" Height="24">
    <Grid>
        <Rectangle Width="18" Height="2" Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                   VerticalAlignment="Top" Margin="0,6,0,0" RadiusX="1" RadiusY="1"/>
        <Rectangle Width="18" Height="2" Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                   VerticalAlignment="Center" RadiusX="1" RadiusY="1"/>
        <Rectangle Width="18" Height="2" Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                   VerticalAlignment="Bottom" Margin="0,0,0,6" RadiusX="1" RadiusY="1"/>
    </Grid>
</UserControl>
