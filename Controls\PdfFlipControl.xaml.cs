using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using FlipBookApp.Services;
using FlipBookApp.ViewModels;

namespace FlipBookApp.Controls
{
    public partial class PdfFlipControl : UserControl
    {
        private MainViewModel? _viewModel;
        private PdfService? _pdfService;

        public PdfFlipControl()
        {
            InitializeComponent();
            Loaded += OnLoaded;
            MouseWheel += OnMouseWheel;
        }

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            _viewModel = DataContext as MainViewModel;
            if (_viewModel != null)
            {
                _pdfService = _viewModel.GetPdfService();
                _viewModel.PropertyChanged += OnViewModelPropertyChanged;

                // Initial page render if PDF is already loaded
                if (_viewModel.IsPdfLoaded)
                {
                    RenderCurrentPage();
                }
            }
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MainViewModel.CurrentPage))
            {
                AnimatePageChange();
            }
            else if (e.PropertyName == nameof(MainViewModel.IsPdfLoaded))
            {
                if (_viewModel?.IsPdfLoaded == true)
                {
                    RenderCurrentPage();
                }
            }
            else if (e.PropertyName == nameof(MainViewModel.ZoomLevel))
            {
                // Re-render page at new zoom level for better quality
                RenderCurrentPage();
            }
        }

        private void AnimatePageChange()
        {
            if (_viewModel == null || !_viewModel.IsPdfLoaded)
                return;

            // Start flip animation
            var flipStoryboard = FindResource("FlipToNextPage") as Storyboard;
            if (flipStoryboard != null)
            {
                flipStoryboard.Completed += OnFlipAnimationCompleted;
                flipStoryboard.Begin();
            }
            else
            {
                // Fallback if animation not found
                RenderCurrentPage();
            }
        }

        private void OnFlipAnimationCompleted(object? sender, EventArgs e)
        {
            // Render the new page
            RenderCurrentPage();

            // Show the page with animation
            var showStoryboard = FindResource("ShowPage") as Storyboard;
            showStoryboard?.Begin();

            // Remove event handler
            if (sender is Storyboard storyboard)
            {
                storyboard.Completed -= OnFlipAnimationCompleted;
            }
        }

        private void RenderCurrentPage()
        {
            if (_viewModel == null || _pdfService == null || !_viewModel.IsPdfLoaded)
                return;

            try
            {
                var pageIndex = _viewModel.CurrentPage - 1; // Convert to 0-based index

                // Render at fixed size but with quality based on zoom level
                // This keeps the display size consistent while improving text quality
                var renderWidth = 800;   // Fixed display width
                var renderHeight = 600;  // Fixed display height
                var qualityMultiplier = _viewModel.ZoomLevel; // Use zoom level for quality

                var pageImage = _pdfService.RenderPage(pageIndex, renderWidth, renderHeight, qualityMultiplier);

                if (pageImage != null)
                {
                    var bitmapSource = ConvertToBitmapSource(pageImage);
                    PdfPageImage.Source = bitmapSource;

                    // Don't set explicit size - let ScaleTransform handle zoom
                    // The image is rendered at fixed size, quality improves with zoom

                    pageImage.Dispose();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering page: {ex.Message}");
            }
        }

        private void OnMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (_viewModel == null) return;

            // Only handle zoom when Ctrl is pressed
            if (Keyboard.Modifiers.HasFlag(ModifierKeys.Control))
            {
                if (e.Delta > 0 && _viewModel.ZoomInCommand.CanExecute(null))
                {
                    _viewModel.ZoomInCommand.Execute(null);
                }
                else if (e.Delta < 0 && _viewModel.ZoomOutCommand.CanExecute(null))
                {
                    _viewModel.ZoomOutCommand.Execute(null);
                }
                e.Handled = true;
            }
            // If Ctrl is not pressed, let the ScrollViewer handle normal scrolling
        }

        private BitmapSource ConvertToBitmapSource(System.Drawing.Image image)
        {
            using var memory = new MemoryStream();
            image.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
            memory.Position = 0;

            var bitmapImage = new BitmapImage();
            bitmapImage.BeginInit();
            bitmapImage.StreamSource = memory;
            bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
            bitmapImage.EndInit();
            bitmapImage.Freeze();

            return bitmapImage;
        }
    }
}
