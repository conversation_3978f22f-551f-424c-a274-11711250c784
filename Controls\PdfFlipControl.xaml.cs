using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using FlipBookApp.Services;
using FlipBookApp.ViewModels;

namespace FlipBookApp.Controls
{
    public partial class PdfFlipControl : UserControl
    {
        private MainViewModel? _viewModel;
        private PdfService? _pdfService;

        public PdfFlipControl()
        {
            InitializeComponent();
            Loaded += OnLoaded;
            MouseWheel += OnMouseWheel;
        }

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            _viewModel = DataContext as MainViewModel;
            if (_viewModel != null)
            {
                _pdfService = _viewModel.GetPdfService();
                _viewModel.PropertyChanged += OnViewModelPropertyChanged;

                // Initial page render if PDF is already loaded
                if (_viewModel.IsPdfLoaded)
                {
                    RenderCurrentPage();
                }
            }
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MainViewModel.CurrentPage))
            {
                AnimatePageChange();
            }
            else if (e.PropertyName == nameof(MainViewModel.IsPdfLoaded))
            {
                if (_viewModel?.IsPdfLoaded == true)
                {
                    RenderCurrentPage();
                }
            }
            else if (e.PropertyName == nameof(MainViewModel.ZoomLevel))
            {
                // Re-render page at new zoom level for better quality
                RenderCurrentPage();

                // Update ScrollViewer to handle new zoom level
                UpdateScrollViewerForZoom();
            }
        }

        private void AnimatePageChange()
        {
            if (_viewModel == null || !_viewModel.IsPdfLoaded)
                return;

            // Start flip animation
            var flipStoryboard = FindResource("FlipToNextPage") as Storyboard;
            if (flipStoryboard != null)
            {
                flipStoryboard.Completed += OnFlipAnimationCompleted;
                flipStoryboard.Begin();
            }
            else
            {
                // Fallback if animation not found
                RenderCurrentPage();
            }
        }

        private void OnFlipAnimationCompleted(object? sender, EventArgs e)
        {
            // Render the new page
            RenderCurrentPage();

            // Show the page with animation
            var showStoryboard = FindResource("ShowPage") as Storyboard;
            showStoryboard?.Begin();

            // Remove event handler
            if (sender is Storyboard storyboard)
            {
                storyboard.Completed -= OnFlipAnimationCompleted;
            }
        }

        private void RenderCurrentPage()
        {
            if (_viewModel == null || _pdfService == null || !_viewModel.IsPdfLoaded)
                return;

            try
            {
                var pageIndex = _viewModel.CurrentPage - 1; // Convert to 0-based index

                // Render at higher resolution based on zoom level for better quality
                // Then scale down to base size for consistent display
                var baseWidth = 800;   // Base display width
                var baseHeight = 600;  // Base display height
                var qualityMultiplier = _viewModel.ZoomLevel; // Use zoom level for quality

                // Render at higher resolution for better quality
                var renderWidth = (int)(baseWidth * qualityMultiplier);
                var renderHeight = (int)(baseHeight * qualityMultiplier);

                var pageImage = _pdfService.RenderPage(pageIndex, renderWidth, renderHeight, 1.0);

                if (pageImage != null)
                {
                    var bitmapSource = ConvertToBitmapSource(pageImage);
                    PdfPageImage.Source = bitmapSource;

                    // Scale the high-resolution image back to base size
                    // This gives us high quality content at the base display size
                    PdfPageImage.Width = baseWidth;
                    PdfPageImage.Height = baseHeight;

                    pageImage.Dispose();

                    // Update ScrollViewer after image is loaded
                    Dispatcher.BeginInvoke(() => UpdateScrollViewerForZoom());
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering page: {ex.Message}");
            }
        }

        private void OnMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (_viewModel == null) return;

            // Only handle zoom when Ctrl is pressed
            if (Keyboard.Modifiers.HasFlag(ModifierKeys.Control))
            {
                if (e.Delta > 0 && _viewModel.ZoomInCommand.CanExecute(null))
                {
                    _viewModel.ZoomInCommand.Execute(null);
                }
                else if (e.Delta < 0 && _viewModel.ZoomOutCommand.CanExecute(null))
                {
                    _viewModel.ZoomOutCommand.Execute(null);
                }
                e.Handled = true;
            }
            // If Ctrl is not pressed, let the ScrollViewer handle normal scrolling
        }

        private void UpdateScrollViewerForZoom()
        {
            if (_viewModel == null || PdfPageImage.Source == null) return;

            // Calculate the actual size of the zoomed content
            var imageWidth = PdfPageImage.Width;
            var imageHeight = PdfPageImage.Height;
            var zoomLevel = _viewModel.ZoomLevel;

            // Find the canvas container and set its size to match the zoomed content size
            var container = FindName("PdfContainer") as Canvas;
            if (container != null)
            {
                // Set canvas size to accommodate the zoomed image
                container.Width = imageWidth * zoomLevel;
                container.Height = imageHeight * zoomLevel;

                // Center the image in the canvas
                Canvas.SetLeft(PdfPageImage, (container.Width - imageWidth) / 2);
                Canvas.SetTop(PdfPageImage, (container.Height - imageHeight) / 2);
            }

            // Force ScrollViewer to update its layout and scrollbars
            PdfScrollViewer.UpdateLayout();
        }

        private BitmapSource ConvertToBitmapSource(System.Drawing.Image image)
        {
            using var memory = new MemoryStream();
            image.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
            memory.Position = 0;

            var bitmapImage = new BitmapImage();
            bitmapImage.BeginInit();
            bitmapImage.StreamSource = memory;
            bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
            bitmapImage.EndInit();
            bitmapImage.Freeze();

            return bitmapImage;
        }
    }
}
