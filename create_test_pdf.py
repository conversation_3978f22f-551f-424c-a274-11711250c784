#!/usr/bin/env python3
"""
Simple script to create a test PDF for the FlipBook application.
Requires: pip install reportlab
"""

from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

def create_test_pdf():
    # Create the PDF document
    doc = SimpleDocTemplate("test_catalog.pdf", pagesize=letter)
    
    # Get styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    
    chapter_style = ParagraphStyle(
        'ChapterTitle',
        parent=styles['Heading2'],
        fontSize=18,
        spaceAfter=20,
        textColor='darkblue'
    )
    
    course_style = ParagraphStyle(
        'CourseTitle',
        parent=styles['Heading3'],
        fontSize=14,
        spaceAfter=15,
        leftIndent=20
    )
    
    # Build the content
    story = []
    
    # Title page
    story.append(Paragraph("Training Catalog", title_style))
    story.append(Spacer(1, 0.5*inch))
    story.append(Paragraph("Professional Development Courses", styles['Normal']))
    story.append(PageBreak())
    
    # Chapter 1: Développement du personnel
    story.append(Paragraph("Chapter 1: Développement du personnel", chapter_style))
    
    courses_ch1 = [
        "La communication professionnelle",
        "Concevoir et déployer le plan de communication", 
        "Connaître son environnement réglementaire RH",
        "Développer son leadership",
        "Entretien d'évaluation et de développement"
    ]
    
    for course in courses_ch1:
        story.append(Paragraph(course, course_style))
        story.append(Spacer(1, 0.2*inch))
        story.append(Paragraph("Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.", styles['Normal']))
        story.append(PageBreak())
    
    # Chapter 2: Vente, Accueil Client & Marketing
    story.append(Paragraph("Chapter 2: Vente, Accueil Client & Marketing", chapter_style))
    
    courses_ch2 = [
        "Animation d'un réseau de vente",
        "Conquête et fidélisation des clients",
        "Définir et déployer son plan marketing",
        "Développer et gérer son portefeuille client"
    ]
    
    for course in courses_ch2:
        story.append(Paragraph(course, course_style))
        story.append(Spacer(1, 0.2*inch))
        story.append(Paragraph("Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.", styles['Normal']))
        story.append(PageBreak())
    
    # Build the PDF
    doc.build(story)
    print("Test PDF created: test_catalog.pdf")

if __name__ == "__main__":
    try:
        create_test_pdf()
    except ImportError:
        print("Please install reportlab: pip install reportlab")
    except Exception as e:
        print(f"Error creating PDF: {e}")
