# How to Run the PDF FlipBook Viewer

## ✅ **Issue Fixed!**

The Visual Studio debug executable path issue has been resolved. Here are multiple ways to run the application:

## **Method 1: Using Visual Studio (Recommended)**

1. **Open the solution**: Double-click `FlipBookApp.sln`
2. **Set startup project**: Right-click on `FlipBookApp` project → "Set as Startup Project"
3. **Run**: Press `F5` or click the "Start" button
4. **Alternative**: Use `Ctrl+F5` to run without debugging

## **Method 2: Using Command Line**

```bash
# Navigate to the project folder
cd D:\Projects\flip

# Build and run
dotnet build
dotnet run
```

## **Method 3: Using Batch File (Windows)**

Simply double-click `run.bat` - it will:
- Build the application if needed
- Launch the executable
- Show status messages

## **Method 4: Using PowerShell**

Right-click `run.ps1` → "Run with PowerShell"

## **Method 5: Direct Executable**

Navigate to `bin\Debug\net8.0-windows\` and double-click `FlipBookApp.exe`

## **Visual Studio Configuration Details**

The project now includes:
- ✅ **Solution file** (`FlipBookApp.sln`)
- ✅ **Launch profiles** (`Properties/launchSettings.json`)
- ✅ **Correct platform target** (x64)
- ✅ **Proper output paths**

## **If You Still Get the Debug Executable Error:**

1. **Clean and rebuild**:
   ```bash
   dotnet clean
   dotnet build
   ```

2. **Check Visual Studio startup project**:
   - Right-click solution → Properties
   - Set "FlipBookApp" as startup project

3. **Verify platform configuration**:
   - Build → Configuration Manager
   - Ensure "Any CPU" or "x64" is selected

## **Application Features Ready to Test:**

- 🖥️ **Dynamic window sizing** (starts maximized)
- 📄 **PDF loading** (with proper DLL dependencies)
- 📚 **Table of contents** (sidebar navigation)
- 🔄 **Page flipping animations**
- 🎨 **Material Design UI**

## **Test Files Included:**

- `test.pdf` - Sample PDF for testing
- `table_content.json` - Table of contents configuration

## **System Requirements:**

- Windows 10/11 (64-bit)
- .NET 8.0 Runtime
- Visual C++ Redistributable 2015-2022 (x64)

## **Need Help?**

- Check `TROUBLESHOOTING.md` for common issues
- Ensure all dependencies are installed
- Try running as administrator if needed

**The application is now ready to run from Visual Studio! 🚀**
