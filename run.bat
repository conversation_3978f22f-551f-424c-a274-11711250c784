@echo off
echo Starting PDF FlipBook Viewer...
echo.

REM Check if the executable exists
if not exist "bin\Debug\net8.0-windows\FlipBookApp.exe" (
    echo Building the application...
    dotnet build
    if errorlevel 1 (
        echo Build failed! Please check the error messages above.
        pause
        exit /b 1
    )
)

REM Run the application
echo Launching FlipBookApp...
start "" "bin\Debug\net8.0-windows\FlipBookApp.exe"

echo Application started successfully!
echo You can close this window.
pause
